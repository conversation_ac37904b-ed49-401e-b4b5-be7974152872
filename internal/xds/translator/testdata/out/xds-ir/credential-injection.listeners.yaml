- address:
    socketAddress:
      address: 0.0.0.0
      portValue: 10080
  defaultFilter<PERSON>hain:
    filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        commonHttpProtocolOptions:
          headersWithUnderscoresAction: REJECT_REQUEST
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
          maxConcurrentStreams: 100
        httpFilters:
        - disabled: true
          name: envoy.filters.http.credential_injector/httproutefilter/default/credential-injection-1
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.credential_injector.v3.CredentialInjector
            credential:
              name: envoy.http.injected_credentials.generic
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.http.injected_credentials.generic.v3.Generic
                credential:
                  name: credential_injector/credential/httproutefilter/default/credential-injection-1
                  sdsConfig:
                    ads: {}
                    resourceApiVersion: V3
        - disabled: true
          name: envoy.filters.http.credential_injector/httproutefilter/default/credential-injection-2
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.credential_injector.v3.CredentialInjector
            credential:
              name: envoy.http.injected_credentials.generic
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.http.injected_credentials.generic.v3.Generic
                credential:
                  name: credential_injector/credential/httproutefilter/default/credential-injection-2
                  sdsConfig:
                    ads: {}
                    resourceApiVersion: V3
                header: x-credential
            overwrite: true
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            suppressEnvoyHeaders: true
        mergeSlashes: true
        normalizePath: true
        pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: envoy-gateway/gateway-1/http
        serverHeaderTransformation: PASS_THROUGH
        statPrefix: http-10080
        useRemoteAddress: true
    name: envoy-gateway/gateway-1/http
  maxConnectionsToAcceptPerSocketEvent: 1
  name: envoy-gateway/gateway-1/http
  perConnectionBufferLimitBytes: 32768

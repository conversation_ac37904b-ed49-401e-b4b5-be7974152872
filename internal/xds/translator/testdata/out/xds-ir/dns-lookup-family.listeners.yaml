- address:
    socketAddress:
      address: 0.0.0.0
      portValue: 19003
  bypassOverloadManager: true
  filterChains:
  - filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        httpFilters:
        - name: envoy.filters.http.health_check
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.health_check.v3.HealthCheck
            headers:
            - name: :path
              stringMatch:
                exact: /ready
            passThroughMode: false
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            suppressEnvoyHeaders: true
        routeConfig:
          name: ready_route
          virtualHosts:
          - domains:
            - '*'
            name: ready_route
            routes:
            - directResponse:
                status: 500
              match:
                prefix: /
        statPrefix: eg-ready-http
  name: envoy-gateway-proxy-ready-0.0.0.0-19003
- accessLog:
  - filter:
      responseFlagFilter:
        flags:
        - NR
    name: envoy.access_loggers.file
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
      logFormat:
        textFormatSource:
          inlineString: |
            [%START_TIME%] "%REQ(:METHOD)% %PROTOCOL%" %RESPONSE_CODE% %RESPONSE_FLAGS% %BYTES_RECEIVED% %BYTES_SENT% %DURATION% %RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)% "%REQ(X-FORWARDED-FOR)%" "%REQ(USER-AGENT)%" "%REQ(X-REQUEST-ID)%" "%REQ(:AUTHORITY)%" "%UPSTREAM_HOST%"\n
      path: /dev/stdout
  - filter:
      responseFlagFilter:
        flags:
        - NR
    name: envoy.access_loggers.open_telemetry
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.access_loggers.open_telemetry.v3.OpenTelemetryAccessLogConfig
      attributes:
        values:
        - key: k8s.namespace.name
          value:
            stringValue: '%ENVIRONMENT(ENVOY_POD_NAMESPACE)%'
        - key: k8s.pod.name
          value:
            stringValue: '%ENVIRONMENT(ENVOY_POD_NAME)%'
      body:
        stringValue: |
          [%START_TIME%] "%REQ(:METHOD)% %PROTOCOL%" %RESPONSE_CODE% %RESPONSE_FLAGS% %BYTES_RECEIVED% %BYTES_SENT% %DURATION% %RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)% "%REQ(X-FORWARDED-FOR)%" "%REQ(USER-AGENT)%" "%REQ(X-REQUEST-ID)%" "%REQ(:AUTHORITY)%" "%UPSTREAM_HOST%"\n
      commonConfig:
        grpcService:
          envoyGrpc:
            clusterName: accesslog_otel_0_1
        logName: otel_envoy_accesslog
        transportApiVersion: V3
      resourceAttributes:
        values:
        - key: k8s.cluster.name
          value:
            stringValue: cluster-1
  address:
    socketAddress:
      address: 0.0.0.0
      portValue: 10080
  defaultFilterChain:
    filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        accessLog:
        - name: envoy.access_loggers.file
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
            logFormat:
              textFormatSource:
                inlineString: |
                  [%START_TIME%] "%REQ(:METHOD)% %PROTOCOL%" %RESPONSE_CODE% %RESPONSE_FLAGS% %BYTES_RECEIVED% %BYTES_SENT% %DURATION% %RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)% "%REQ(X-FORWARDED-FOR)%" "%REQ(USER-AGENT)%" "%REQ(X-REQUEST-ID)%" "%REQ(:AUTHORITY)%" "%UPSTREAM_HOST%"\n
            path: /dev/stdout
        - name: envoy.access_loggers.open_telemetry
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.access_loggers.open_telemetry.v3.OpenTelemetryAccessLogConfig
            attributes:
              values:
              - key: k8s.namespace.name
                value:
                  stringValue: '%ENVIRONMENT(ENVOY_POD_NAMESPACE)%'
              - key: k8s.pod.name
                value:
                  stringValue: '%ENVIRONMENT(ENVOY_POD_NAME)%'
            body:
              stringValue: |
                [%START_TIME%] "%REQ(:METHOD)% %PROTOCOL%" %RESPONSE_CODE% %RESPONSE_FLAGS% %BYTES_RECEIVED% %BYTES_SENT% %DURATION% %RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)% "%REQ(X-FORWARDED-FOR)%" "%REQ(USER-AGENT)%" "%REQ(X-REQUEST-ID)%" "%REQ(:AUTHORITY)%" "%UPSTREAM_HOST%"\n
            commonConfig:
              grpcService:
                envoyGrpc:
                  clusterName: accesslog_otel_0_1
              logName: otel_envoy_accesslog
              transportApiVersion: V3
            resourceAttributes:
              values:
              - key: k8s.cluster.name
                value:
                  stringValue: cluster-1
        commonHttpProtocolOptions:
          headersWithUnderscoresAction: REJECT_REQUEST
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
          maxConcurrentStreams: 100
        httpFilters:
        - disabled: true
          name: envoy.filters.http.ext_authz/securitypolicy/envoy-gateway/policy-for-gateway-1
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.ext_authz.v3.ExtAuthz
            httpService:
              serverUri:
                cluster: securitypolicy/envoy-gateway/policy-for-gateway-1/extauth/0
                timeout: 10s
                uri: http://backend-v3.gateway-conformance-infra.svc.cluster.local:8080
            transportApiVersion: V3
            withRequestBody:
              maxRequestBytes: 8192
        - name: envoy.filters.http.grpc_web
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.grpc_web.v3.GrpcWeb
        - name: envoy.filters.http.grpc_stats
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.grpc_stats.v3.FilterConfig
            emitFilterState: true
            statsForAllMethods: true
        - disabled: true
          name: envoy.filters.http.ext_proc/envoyextensionpolicy/default/policy-for-httproute/extproc/0
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
            grpcService:
              envoyGrpc:
                authority: backend-fqdn2.default:9090
                clusterName: envoyextensionpolicy/default/policy-for-httproute/extproc/0
              timeout: 10s
            processingMode:
              requestHeaderMode: SKIP
              requestTrailerMode: SKIP
              responseHeaderMode: SKIP
              responseTrailerMode: SKIP
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            suppressEnvoyHeaders: true
        mergeSlashes: true
        normalizePath: true
        pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: envoy-gateway/gateway-1/http
        serverHeaderTransformation: PASS_THROUGH
        statPrefix: http-10080
        useRemoteAddress: true
    name: envoy-gateway/gateway-1/http
  maxConnectionsToAcceptPerSocketEvent: 1
  name: envoy-gateway/gateway-1/http
  perConnectionBufferLimitBytes: 32768

- address:
    socketAddress:
      address: 0.0.0.0
      portValue: 10080
  defaultFilter<PERSON>hain:
    filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        commonHttpProtocolOptions:
          headersWithUnderscoresAction: REJECT_REQUEST
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
          maxConcurrentStreams: 100
        httpFilters:
        - disabled: true
          name: envoy.filters.http.ext_authz/securitypolicy/default/policy-for-http-route-1
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.ext_authz.v3.ExtAuthz
            allowedHeaders:
              patterns:
              - exact: header1
                ignoreCase: true
              - exact: header2
                ignoreCase: true
            grpcService:
              envoyGrpc:
                authority: primary.foo.com
                clusterName: securitypolicy/default/policy-for-http-route-1/default/grpc-backend
              timeout: 10s
            transportApiVersion: V3
        - disabled: true
          name: envoy.filters.http.ext_authz/securitypolicy/default/policy-for-gateway-1
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.ext_authz.v3.ExtAuthz
            failureModeAllow: true
            httpService:
              authorizationResponse:
                allowedUpstreamHeaders:
                  patterns:
                  - exact: header1
                    ignoreCase: true
                  - exact: header2
                    ignoreCase: true
              pathPrefix: /auth
              serverUri:
                cluster: securitypolicy/default/policy-for-gateway-1/envoy-gateway/http-backend
                timeout: 10s
                uri: http://primary.foo.com/auth
            transportApiVersion: V3
            withRequestBody:
              maxRequestBytes: 32768
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            suppressEnvoyHeaders: true
        mergeSlashes: true
        normalizePath: true
        pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: default/gateway-1/http
        serverHeaderTransformation: PASS_THROUGH
        statPrefix: http-10080
        useRemoteAddress: true
    name: default/gateway-1/http
  maxConnectionsToAcceptPerSocketEvent: 1
  name: default/gateway-1/http
  perConnectionBufferLimitBytes: 32768

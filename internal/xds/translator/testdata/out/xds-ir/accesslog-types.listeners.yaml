- accessLog:
  - name: envoy.access_loggers.file
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
      logFormat:
        textFormatSource:
          inlineString: |
            this is a listener log
      path: /dev/stdout
  - filter:
      responseFlagFilter:
        flags:
        - NR
    name: envoy.access_loggers.file
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
      logFormat:
        textFormatSource:
          inlineString: |
            this is a Global log
      path: /dev/stdout
  - filter:
      responseFlagFilter:
        flags:
        - NR
    name: envoy.access_loggers.file
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
      logFormat:
        jsonFormat:
          :authority: '%REQ(:AUTHORITY)%'
          bytes_received: '%BYTES_RECEIVED%'
          bytes_sent: '%BYTES_SENT%'
          connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
          downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
          downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
          duration: '%DURATION%'
          method: '%REQ(:METHOD)%'
          protocol: '%PROTOCOL%'
          requested_server_name: '%REQUESTED_SERVER_NAME%'
          response_code: '%RESPONSE_CODE%'
          response_code_details: '%RESPONSE_CODE_DETAILS%'
          response_flags: '%RESPONSE_FLAGS%'
          route_name: '%ROUTE_NAME%'
          start_time: '%START_TIME%'
          upstream_cluster: '%UPSTREAM_CLUSTER%'
          upstream_host: '%UPSTREAM_HOST%'
          upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
          upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
          user-agent: '%REQ(USER-AGENT)%'
          x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
          x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
          x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
          x-request-id: '%REQ(X-REQUEST-ID)%'
      path: /dev/stdout
  - name: envoy.access_loggers.http_grpc
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.access_loggers.grpc.v3.HttpGrpcAccessLogConfig
      additionalRequestHeadersToLog:
      - x-client-ip-address
      additionalResponseHeadersToLog:
      - cache-control
      additionalResponseTrailersToLog:
      - expires
      commonConfig:
        grpcService:
          envoyGrpc:
            clusterName: accesslog_als_1_1
        logName: accesslog
        transportApiVersion: V3
  - name: envoy.access_loggers.tcp_grpc
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.access_loggers.grpc.v3.TcpGrpcAccessLogConfig
      commonConfig:
        grpcService:
          envoyGrpc:
            clusterName: accesslog_als_1_2
        logName: envoy-gateway-system/test
        transportApiVersion: V3
  - filter:
      responseFlagFilter:
        flags:
        - NR
    name: envoy.access_loggers.http_grpc
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.access_loggers.grpc.v3.HttpGrpcAccessLogConfig
      additionalRequestHeadersToLog:
      - x-client-ip-address
      additionalResponseHeadersToLog:
      - cache-control
      additionalResponseTrailersToLog:
      - expires
      commonConfig:
        grpcService:
          envoyGrpc:
            clusterName: accesslog_als_2_1
        logName: accesslog
        transportApiVersion: V3
  - filter:
      responseFlagFilter:
        flags:
        - NR
    name: envoy.access_loggers.tcp_grpc
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.access_loggers.grpc.v3.TcpGrpcAccessLogConfig
      commonConfig:
        grpcService:
          envoyGrpc:
            clusterName: accesslog_als_2_2
        logName: envoy-gateway-system/test
        transportApiVersion: V3
  - name: envoy.access_loggers.open_telemetry
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.access_loggers.open_telemetry.v3.OpenTelemetryAccessLogConfig
      attributes:
        values:
        - key: k8s.namespace.name
          value:
            stringValue: '%ENVIRONMENT(ENVOY_POD_NAMESPACE)%'
        - key: k8s.pod.name
          value:
            stringValue: '%ENVIRONMENT(ENVOY_POD_NAME)%'
      body:
        stringValue: |
          this is a listener log
      commonConfig:
        grpcService:
          envoyGrpc:
            authority: otel-collector.monitoring.svc.cluster.local
            clusterName: accesslog_otel_1_3
        logName: otel_envoy_accesslog
        transportApiVersion: V3
      resourceAttributes:
        values:
        - key: k8s.cluster.name
          value:
            stringValue: cluster-1
  - filter:
      responseFlagFilter:
        flags:
        - NR
    name: envoy.access_loggers.open_telemetry
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.access_loggers.open_telemetry.v3.OpenTelemetryAccessLogConfig
      attributes:
        values:
        - key: k8s.namespace.name
          value:
            stringValue: '%ENVIRONMENT(ENVOY_POD_NAMESPACE)%'
        - key: k8s.pod.name
          value:
            stringValue: '%ENVIRONMENT(ENVOY_POD_NAME)%'
      body:
        stringValue: |
          this is a Global log
      commonConfig:
        grpcService:
          envoyGrpc:
            authority: otel-collector.monitoring.svc.cluster.local
            clusterName: accesslog_otel_2_3
        logName: otel_envoy_accesslog
        transportApiVersion: V3
      resourceAttributes:
        values:
        - key: k8s.cluster.name
          value:
            stringValue: cluster-1
  address:
    socketAddress:
      address: 0.0.0.0
      portValue: 10080
  defaultFilterChain:
    filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        accessLog:
        - name: envoy.access_loggers.file
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
            logFormat:
              textFormatSource:
                inlineString: |
                  this is a route log
            path: /dev/stdout
        - name: envoy.access_loggers.file
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
            logFormat:
              textFormatSource:
                inlineString: |
                  this is a Global log
            path: /dev/stdout
        - name: envoy.access_loggers.file
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
            logFormat:
              jsonFormat:
                :authority: '%REQ(:AUTHORITY)%'
                bytes_received: '%BYTES_RECEIVED%'
                bytes_sent: '%BYTES_SENT%'
                connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                duration: '%DURATION%'
                method: '%REQ(:METHOD)%'
                protocol: '%PROTOCOL%'
                requested_server_name: '%REQUESTED_SERVER_NAME%'
                response_code: '%RESPONSE_CODE%'
                response_code_details: '%RESPONSE_CODE_DETAILS%'
                response_flags: '%RESPONSE_FLAGS%'
                route_name: '%ROUTE_NAME%'
                start_time: '%START_TIME%'
                upstream_cluster: '%UPSTREAM_CLUSTER%'
                upstream_host: '%UPSTREAM_HOST%'
                upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                user-agent: '%REQ(USER-AGENT)%'
                x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                x-request-id: '%REQ(X-REQUEST-ID)%'
            path: /dev/stdout
        - name: envoy.access_loggers.http_grpc
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.access_loggers.grpc.v3.HttpGrpcAccessLogConfig
            additionalRequestHeadersToLog:
            - x-client-ip-address
            additionalResponseHeadersToLog:
            - cache-control
            additionalResponseTrailersToLog:
            - expires
            commonConfig:
              grpcService:
                envoyGrpc:
                  clusterName: accesslog_als_0_1
              logName: accesslog
              transportApiVersion: V3
        - name: envoy.access_loggers.tcp_grpc
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.access_loggers.grpc.v3.TcpGrpcAccessLogConfig
            commonConfig:
              grpcService:
                envoyGrpc:
                  clusterName: accesslog_als_0_2
              logName: envoy-gateway-system/test
              transportApiVersion: V3
        - name: envoy.access_loggers.http_grpc
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.access_loggers.grpc.v3.HttpGrpcAccessLogConfig
            additionalRequestHeadersToLog:
            - x-client-ip-address
            additionalResponseHeadersToLog:
            - cache-control
            additionalResponseTrailersToLog:
            - expires
            commonConfig:
              grpcService:
                envoyGrpc:
                  clusterName: accesslog_als_2_1
              logName: accesslog
              transportApiVersion: V3
        - name: envoy.access_loggers.tcp_grpc
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.access_loggers.grpc.v3.TcpGrpcAccessLogConfig
            commonConfig:
              grpcService:
                envoyGrpc:
                  clusterName: accesslog_als_2_2
              logName: envoy-gateway-system/test
              transportApiVersion: V3
        - name: envoy.access_loggers.open_telemetry
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.access_loggers.open_telemetry.v3.OpenTelemetryAccessLogConfig
            attributes:
              values:
              - key: k8s.namespace.name
                value:
                  stringValue: '%ENVIRONMENT(ENVOY_POD_NAMESPACE)%'
              - key: k8s.pod.name
                value:
                  stringValue: '%ENVIRONMENT(ENVOY_POD_NAME)%'
            body:
              stringValue: |
                this is a route log
            commonConfig:
              grpcService:
                envoyGrpc:
                  authority: otel-collector.monitoring.svc.cluster.local
                  clusterName: accesslog_otel_0_3
              logName: otel_envoy_accesslog
              transportApiVersion: V3
            resourceAttributes:
              values:
              - key: k8s.cluster.name
                value:
                  stringValue: cluster-1
        - name: envoy.access_loggers.open_telemetry
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.access_loggers.open_telemetry.v3.OpenTelemetryAccessLogConfig
            attributes:
              values:
              - key: k8s.namespace.name
                value:
                  stringValue: '%ENVIRONMENT(ENVOY_POD_NAMESPACE)%'
              - key: k8s.pod.name
                value:
                  stringValue: '%ENVIRONMENT(ENVOY_POD_NAME)%'
            body:
              stringValue: |
                this is a Global log
            commonConfig:
              grpcService:
                envoyGrpc:
                  authority: otel-collector.monitoring.svc.cluster.local
                  clusterName: accesslog_otel_2_3
              logName: otel_envoy_accesslog
              transportApiVersion: V3
            resourceAttributes:
              values:
              - key: k8s.cluster.name
                value:
                  stringValue: cluster-1
        commonHttpProtocolOptions:
          headersWithUnderscoresAction: REJECT_REQUEST
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
          maxConcurrentStreams: 100
        httpFilters:
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            suppressEnvoyHeaders: true
        mergeSlashes: true
        normalizePath: true
        pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: envoy-gateway/gateway-1/http
        serverHeaderTransformation: PASS_THROUGH
        statPrefix: http-10080
        useRemoteAddress: true
    name: envoy-gateway/gateway-1/http
  maxConnectionsToAcceptPerSocketEvent: 1
  name: envoy-gateway/gateway-1/http
  perConnectionBufferLimitBytes: 32768

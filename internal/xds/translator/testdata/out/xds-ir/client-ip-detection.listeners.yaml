- address:
    socketAddress:
      address: '::'
      portValue: 8081
  defaultFilterChain:
    filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        commonHttpProtocolOptions:
          headersWithUnderscoresAction: REJECT_REQUEST
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
          maxConcurrentStreams: 100
        httpFilters:
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            suppressEnvoyHeaders: true
        normalizePath: true
        originalIpDetectionExtensions:
        - name: envoy.extensions.http.original_ip_detection.xff
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.http.original_ip_detection.xff.v3.XffConfig
            skipXffAppend: false
            xffNumTrustedHops: 1
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: first-listener
        serverHeaderTransformation: PASS_THROUGH
        statPrefix: http-8081
        useRemoteAddress: false
    name: first-listener
  maxConnectionsToAcceptPerSocketEvent: 1
  name: first-listener
  perConnectionBufferLimitBytes: 32768
- address:
    socketAddress:
      address: '::'
      portValue: 8082
  defaultFilterChain:
    filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        commonHttpProtocolOptions:
          headersWithUnderscoresAction: REJECT_REQUEST
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
          maxConcurrentStreams: 100
        httpFilters:
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            suppressEnvoyHeaders: true
        normalizePath: true
        originalIpDetectionExtensions:
        - name: envoy.extensions.http.original_ip_detection.custom_header
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.http.original_ip_detection.custom_header.v3.CustomHeaderConfig
            allowExtensionToSetAddressAsTrusted: true
            headerName: x-my-custom-header
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: second-listener
        serverHeaderTransformation: PASS_THROUGH
        statPrefix: http-8082
        useRemoteAddress: false
    name: second-listener
  maxConnectionsToAcceptPerSocketEvent: 1
  name: second-listener
  perConnectionBufferLimitBytes: 32768
- address:
    socketAddress:
      address: '::'
      portValue: 8083
  defaultFilterChain:
    filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        commonHttpProtocolOptions:
          headersWithUnderscoresAction: REJECT_REQUEST
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
          maxConcurrentStreams: 100
        httpFilters:
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            suppressEnvoyHeaders: true
        normalizePath: true
        originalIpDetectionExtensions:
        - name: envoy.extensions.http.original_ip_detection.custom_header
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.http.original_ip_detection.custom_header.v3.CustomHeaderConfig
            allowExtensionToSetAddressAsTrusted: true
            headerName: x-my-custom-header
            rejectWithStatus:
              code: Forbidden
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: third-listener
        serverHeaderTransformation: PASS_THROUGH
        statPrefix: http-8083
        useRemoteAddress: false
    name: third-listener
  maxConnectionsToAcceptPerSocketEvent: 1
  name: third-listener
  perConnectionBufferLimitBytes: 32768
- address:
    socketAddress:
      address: '::'
      portValue: 8084
  defaultFilterChain:
    filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        commonHttpProtocolOptions:
          headersWithUnderscoresAction: REJECT_REQUEST
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
          maxConcurrentStreams: 100
        httpFilters:
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            suppressEnvoyHeaders: true
        normalizePath: true
        originalIpDetectionExtensions:
        - name: envoy.extensions.http.original_ip_detection.xff
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.http.original_ip_detection.xff.v3.XffConfig
            skipXffAppend: false
            xffTrustedCidrs:
              cidrs:
              - addressPrefix: ***********
                prefixLen: 24
              - addressPrefix: 10.0.0.0
                prefixLen: 16
              - addressPrefix: **********
                prefixLen: 12
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: fourth-listener
        serverHeaderTransformation: PASS_THROUGH
        statPrefix: http-8084
        useRemoteAddress: false
    name: fourth-listener
  maxConnectionsToAcceptPerSocketEvent: 1
  name: fourth-listener
  perConnectionBufferLimitBytes: 32768

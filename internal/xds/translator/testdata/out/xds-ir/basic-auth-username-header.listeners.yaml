- address:
    socketAddress:
      address: 0.0.0.0
      portValue: 10080
  defaultFilterChain:
    filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        commonHttpProtocolOptions:
          headersWithUnderscoresAction: REJECT_REQUEST
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
          maxConcurrentStreams: 100
        httpFilters:
        - disabled: true
          name: envoy.filters.http.basic_auth/securitypolicy/default/policy-for-http-route-1
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.basic_auth.v3.BasicAuth
            forwardUsernameHeader: x-username
            users:
              inlineBytes: dXNlcjE6e1NIQX10RVNzQm1FL3lOWTNsYjZhMEw2dlZRRVpOcXc9CnVzZXIyOntTSEF9RUo5TFBGRFhzTjl5blNtYnh2anA3NUJtbHg4PQo=
        - disabled: true
          name: envoy.filters.http.basic_auth/securitypolicy/default/policy-for-gateway-1
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.basic_auth.v3.BasicAuth
            forwardUsernameHeader: x-user-id
            users:
              inlineBytes: Zm9vOntTSEF9WXMyM0FnLzVJT1dxWkN3OVFHYVZEZEh3SDAwPQpmb28xOntTSEF9ZGpaMTFxSFkwS09pamV5bUs3YUt2WXV2aHZNPQo=
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            suppressEnvoyHeaders: true
        mergeSlashes: true
        normalizePath: true
        pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: default/gateway-1/http
        serverHeaderTransformation: PASS_THROUGH
        statPrefix: http-10080
        useRemoteAddress: true
    name: default/gateway-1/http
  maxConnectionsToAcceptPerSocketEvent: 1
  name: default/gateway-1/http
  perConnectionBufferLimitBytes: 32768

- address:
    socketAddress:
      address: 0.0.0.0
      portValue: 10080
  defaultFilterChain:
    filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        commonHttpProtocolOptions:
          headersWithUnderscoresAction: REJECT_REQUEST
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
          maxConcurrentStreams: 100
        httpFilters:
        - disabled: true
          name: envoy.filters.http.ext_proc/envoyextensionpolicy/default/policy-for-route-2/extproc/0
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
            allowModeOverride: true
            failureModeAllow: true
            grpcService:
              envoyGrpc:
                authority: grpc-backend-4.default:4000
                clusterName: envoyextensionpolicy/default/policy-for-route-2/0/grpc-backend-4
              timeout: 10s
            messageTimeout: 5s
            metadataOptions:
              forwardingNamespaces:
                untyped:
                - envoy.filters.http.ext_authz
              receivingNamespaces:
                untyped:
                - envoy.filters.http.my_custom
            processingMode:
              requestBodyMode: BUFFERED
              requestHeaderMode: SEND
              requestTrailerMode: SKIP
              responseBodyMode: STREAMED
              responseHeaderMode: SKIP
              responseTrailerMode: SKIP
            requestAttributes:
            - xds.route_metadata
            - connection.requested_server_name
            responseAttributes:
            - request.path
        - disabled: true
          name: envoy.filters.http.ext_proc/envoyextensionpolicy/default/policy-for-route-1/extproc/0
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
            failureModeAllow: true
            grpcService:
              envoyGrpc:
                authority: grpc-backend-2.default:8000
                clusterName: envoyextensionpolicy/default/policy-for-route-1/0/grpc-backend-2
              timeout: 10s
            messageTimeout: 5s
            processingMode:
              requestBodyMode: BUFFERED_PARTIAL
              requestHeaderMode: SKIP
              requestTrailerMode: SKIP
              responseHeaderMode: SEND
              responseTrailerMode: SKIP
        - disabled: true
          name: envoy.filters.http.ext_proc/envoyextensionpolicy/envoy-gateway/policy-for-gateway-2/extproc/0
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
            grpcService:
              envoyGrpc:
                authority: grpc-backend-3.envoy-gateway:3000
                clusterName: envoyextensionpolicy/envoy-gateway/policy-for-gateway-2/0/grpc-backend-3
              timeout: 10s
            processingMode:
              requestHeaderMode: SKIP
              requestTrailerMode: SKIP
              responseHeaderMode: SKIP
              responseTrailerMode: SKIP
        - disabled: true
          name: envoy.filters.http.ext_proc/envoyextensionpolicy/envoy-gateway/policy-for-gateway-1/extproc/0
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
            grpcService:
              envoyGrpc:
                authority: grpc-backend.envoy-gateway:9000
                clusterName: envoyextensionpolicy/envoy-gateway/policy-for-gateway-1/0/grpc-backend
              timeout: 10s
            messageTimeout: 15s
            metadataOptions:
              forwardingNamespaces:
                untyped:
                - envoy.filters.http.ext_proc
              receivingNamespaces:
                untyped:
                - envoy.filters.http.prc_ext
            processingMode:
              requestHeaderMode: SKIP
              requestTrailerMode: SKIP
              responseHeaderMode: SKIP
              responseTrailerMode: SKIP
            requestAttributes:
            - xds.route_metadata
            - connection.requested_server_name
            responseAttributes:
            - request.path
        - disabled: true
          name: envoy.filters.http.ext_proc/envoyextensionpolicy/envoy-gateway/policy-for-route-3/extproc/0
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
            grpcService:
              envoyGrpc:
                authority: grpc-backend-3.envoy-gateway:3000
                clusterName: envoyextensionpolicy/envoy-gateway/policy-for-route-3/0/grpc-backend-3
              timeout: 10s
            processingMode:
              requestBodyMode: FULL_DUPLEX_STREAMED
              requestHeaderMode: SKIP
              requestTrailerMode: SEND
              responseBodyMode: FULL_DUPLEX_STREAMED
              responseHeaderMode: SKIP
              responseTrailerMode: SEND
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            suppressEnvoyHeaders: true
        mergeSlashes: true
        normalizePath: true
        pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: envoy-gateway/gateway-1/http
        serverHeaderTransformation: PASS_THROUGH
        statPrefix: http-10080
        useRemoteAddress: true
    name: envoy-gateway/gateway-1/http
  maxConnectionsToAcceptPerSocketEvent: 1
  name: envoy-gateway/gateway-1/http
  perConnectionBufferLimitBytes: 32768

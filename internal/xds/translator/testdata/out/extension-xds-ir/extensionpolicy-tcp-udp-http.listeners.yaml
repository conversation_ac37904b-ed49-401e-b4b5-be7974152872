- address:
    socketAddress:
      address: 0.0.0.0
      portValue: 10080
  defaultFilterChain:
    filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        commonHttpProtocolOptions:
          headersWithUnderscoresAction: REJECT_REQUEST
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
          maxConcurrentStreams: 100
        httpFilters:
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            suppressEnvoyHeaders: true
        mergeSlashes: true
        normalizePath: true
        pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: envoy-gateway/gateway-1/http1
        serverHeaderTransformation: PASS_THROUGH
        statPrefix: http-10080
        useRemoteAddress: true
    name: envoy-gateway/gateway-1/http1
  filterChains:
  - filters:
    - name: envoy.filters.network.tcp_proxy
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy
        cluster: tcp-route-dest
        statPrefix: tcp-10080
  maxConnectionsToAcceptPerSocketEvent: 1
  name: envoy-gateway/gateway-1/http1
  perConnectionBufferLimitBytes: 32768
  statPrefix: envoy-gateway/gateway-1/http1
- address:
    socketAddress:
      address: 0.0.0.0
      portValue: 10162
      protocol: UDP
  listenerFilters:
  - name: envoy.filters.udp_listener.udp_proxy
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.filters.udp.udp_proxy.v3.UdpProxyConfig
      matcher:
        onNoMatch:
          action:
            name: route
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.filters.udp.udp_proxy.v3.Route
              cluster: udp-route-dest
      statPrefix: service
  name: envoy-gateway/gateway-1/udp1
  statPrefix: envoy-gateway/gateway-1/udp1

- address:
    socketAddress:
      address: 0.0.0.0
      portValue: 10081
  defaultFilterChain:
    filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        commonHttpProtocolOptions:
          headersWithUnderscoresAction: REJECT_REQUEST
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
          maxConcurrentStreams: 100
        httpFilters:
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            suppressEnvoyHeaders: true
        mergeSlashes: true
        normalizePath: true
        pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: policyextension-listener
        serverHeaderTransformation: PASS_THROUGH
        statPrefix: http-10081
        useRemoteAddress: true
    name: policyextension-listener
  maxConnectionsToAcceptPerSocketEvent: 1
  name: policyextension-listener
  perConnectionBufferLimitBytes: 32768
  statPrefix: from-the-policy

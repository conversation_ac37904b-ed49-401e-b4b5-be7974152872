http:
  - name: "first-listener"
    address: "::"
    port: 10080
    hostnames:
      - "*"
    path:
      mergeSlashes: true
      escapedSlashesAction: UnescapeAndRedirect
    routes:
      - name: "first-route"
        hostname: "*"
        destination:
          name: "first-route-dest"
          settings:
            - endpoints:
                - host: "*******"
                  port: 50000
              name: "first-route-dest/backend/0"
    connection:
      maxAcceptPerSocketEvent: 2
tcp:
  - name: "second-listener"
    address: "::"
    connection:
      maxAcceptPerSocketEvent: 0
    port: 10081
    routes:
      - name: "max-accept-disabled"
        destination:
          name: "max-accept-disabled"
          settings:
            - endpoints:
                - host: "*******"
                  port: 50000
              name: "max-accept-disabled/backend/0"
  - name: "third-listener"
    address: "::"
    port: 10082
    routes:
      - name: "max-accept-default"
        destination:
          name: "max-accept-default"
          settings:
            - endpoints:
                - host: "*******"
                  port: 50001
              name: "max-accept-default/backend/0"

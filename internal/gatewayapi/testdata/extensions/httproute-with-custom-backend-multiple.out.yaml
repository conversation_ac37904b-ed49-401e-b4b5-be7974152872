gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      hostname: '*.envoyproxy.io'
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-1
    namespace: default
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - group: storage.example.io
        kind: S3Backend
        name: s3-backend-1
        port: 443
      - group: storage.example.io
        kind: S3Backend
        name: s3-backend-2
        port: 443
      matches:
      - path:
          value: /service
    - backendRefs:
      - group: storage.example.io
        kind: S3Backend
        name: s3-backend-3
        port: 443
      - group: storage.example.io
        kind: S3Backend
        name: s3-backend-4
        port: 443
      matches:
      - path:
          value: /s3
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
infraIR:
  envoy-gateway/gateway-1:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-1/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
        ownerReference:
          kind: GatewayClass
          name: envoy-gateway-class
      name: envoy-gateway/gateway-1
      namespace: ""
xdsIR:
  envoy-gateway/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      hostnames:
      - '*.envoyproxy.io'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
      name: envoy-gateway/gateway-1/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-1
            namespace: default
          name: httproute/default/httproute-1/rule/0
          settings:
          - isCustomBackend: true
            name: httproute/default/httproute-1/rule/0/backend/0
            weight: 1
          - isCustomBackend: true
            name: httproute/default/httproute-1/rule/0/backend/1
            weight: 1
        extensionRefs:
        - object:
            apiVersion: storage.example.io/v1alpha1
            kind: S3Backend
            metadata:
              name: s3-backend-1
              namespace: default
            spec:
              bucket: my-s3-bucket
              endpoint: s3.amazonaws.com
              region: us-west-2
        - object:
            apiVersion: storage.example.io/v1alpha1
            kind: S3Backend
            metadata:
              name: s3-backend-2
              namespace: default
            spec:
              bucket: my-s3-bucket
              endpoint: s3.amazonaws.com
              region: us-west-2
        hostname: gateway.envoyproxy.io
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-1
          namespace: default
        name: httproute/default/httproute-1/rule/0/match/0/gateway_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /service
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-1
            namespace: default
          name: httproute/default/httproute-1/rule/1
          settings:
          - isCustomBackend: true
            name: httproute/default/httproute-1/rule/1/backend/0
            weight: 1
          - isCustomBackend: true
            name: httproute/default/httproute-1/rule/1/backend/1
            weight: 1
        extensionRefs:
        - object:
            apiVersion: storage.example.io/v1alpha1
            kind: S3Backend
            metadata:
              name: s3-backend-3
              namespace: default
            spec:
              bucket: my-s3-bucket
              endpoint: s3.amazonaws.com
              region: us-west-2
        - object:
            apiVersion: storage.example.io/v1alpha1
            kind: S3Backend
            metadata:
              name: s3-backend-4
              namespace: default
            spec:
              bucket: my-s3-bucket
              endpoint: s3.amazonaws.com
              region: us-west-2
        hostname: gateway.envoyproxy.io
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-1
          namespace: default
        name: httproute/default/httproute-1/rule/1/match/0/gateway_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /s3
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003

gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-1
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      hostname: "*.envoyproxy.io"
      allowedRoutes:
        namespaces:
          from: All
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-1
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-1
      sectionName: http
    rules:
    - matches:
      - path:
          value: "/"
      backendRefs:
      - name: service-1
        port: 8080
      filters:
      - type: ExtensionRef
        extensionRef:
          group: foo.example.io
          kind: Foo
          name: test
extensionRefFilters:
- apiVersion: foo.example.io
  kind: Foo
  metadata:
    name: test
    namespace: default
  spec:
    data: "stuff"

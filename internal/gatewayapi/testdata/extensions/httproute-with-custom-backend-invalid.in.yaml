gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-1
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      hostname: "*.envoyproxy.io"
      allowedRoutes:
        namespaces:
          from: All
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-1
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-1
      sectionName: http
    rules:
    - matches:
      - path:
          value: "/"
      backendRefs:
      - group: storage.example.io
        kind: UnsupportedBackend
        name: unsupported-backend
        port: 443
extensionRefFilters:
- apiVersion: storage.example.io/v1alpha1
  kind: UnsupportedBackend
  metadata:
    name: unsupported-backend
    namespace: default
  spec:
    invalidField: invalid-value

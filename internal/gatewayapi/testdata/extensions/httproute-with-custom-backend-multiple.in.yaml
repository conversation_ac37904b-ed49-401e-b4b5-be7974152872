gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-1
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      hostname: "*.envoyproxy.io"
      allowedRoutes:
        namespaces:
          from: All
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-1
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-1
      sectionName: http
    rules:
    - matches:
      - path:
          value: "/service"
      backendRefs:
      - group: storage.example.io
        kind: S3Backend
        name: s3-backend-1
        port: 443
      - group: storage.example.io
        kind: S3Backend
        name: s3-backend-2
        port: 443
    - matches:
      - path:
          value: "/s3"
      backendRefs:
      - group: storage.example.io
        kind: S3Backend
        name: s3-backend-3
        port: 443
      - group: storage.example.io
        kind: S3Backend
        name: s3-backend-4
        port: 443
extensionRefFilters:
- apiVersion: storage.example.io/v1alpha1
  kind: S3Backend
  metadata:
    name: s3-backend-1
    namespace: default
  spec:
    bucket: my-s3-bucket
    region: us-west-2
    endpoint: s3.amazonaws.com
- apiVersion: storage.example.io/v1alpha1
  kind: S3Backend
  metadata:
    name: s3-backend-2
    namespace: default
  spec:
    bucket: my-s3-bucket
    region: us-west-2
    endpoint: s3.amazonaws.com
- apiVersion: storage.example.io/v1alpha1
  kind: S3Backend
  metadata:
    name: s3-backend-3
    namespace: default
  spec:
    bucket: my-s3-bucket
    region: us-west-2
    endpoint: s3.amazonaws.com
- apiVersion: storage.example.io/v1alpha1
  kind: S3Backend
  metadata:
    name: s3-backend-4
    namespace: default
  spec:
    bucket: my-s3-bucket
    region: us-west-2
    endpoint: s3.amazonaws.com

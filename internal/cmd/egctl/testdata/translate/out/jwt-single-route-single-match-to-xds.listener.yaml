xds:
  envoy-gateway-system/eg:
    '@type': type.googleapis.com/envoy.admin.v3.ListenersConfigDump
    dynamicListeners:
    - activeState:
        listener:
          '@type': type.googleapis.com/envoy.config.listener.v3.Listener
          address:
            socketAddress:
              address: 0.0.0.0
              portValue: 19003
          bypassOverloadManager: true
          filterChains:
          - filters:
            - name: envoy.filters.network.http_connection_manager
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                httpFilters:
                - name: envoy.filters.http.health_check
                  typedConfig:
                    '@type': type.googleapis.com/envoy.extensions.filters.http.health_check.v3.HealthCheck
                    headers:
                    - name: :path
                      stringMatch:
                        exact: /ready
                    passThroughMode: false
                - name: envoy.filters.http.router
                  typedConfig:
                    '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                    suppressEnvoyHeaders: true
                routeConfig:
                  name: ready_route
                  virtualHosts:
                  - domains:
                    - '*'
                    name: ready_route
                    routes:
                    - directResponse:
                        status: 500
                      match:
                        prefix: /
                statPrefix: eg-ready-http
          name: envoy-gateway-proxy-ready-0.0.0.0-19003
    - activeState:
        listener:
          '@type': type.googleapis.com/envoy.config.listener.v3.Listener
          accessLog:
          - filter:
              responseFlagFilter:
                flags:
                - NR
            name: envoy.access_loggers.file
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
              logFormat:
                jsonFormat:
                  :authority: '%REQ(:AUTHORITY)%'
                  bytes_received: '%BYTES_RECEIVED%'
                  bytes_sent: '%BYTES_SENT%'
                  connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                  downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                  downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                  duration: '%DURATION%'
                  method: '%REQ(:METHOD)%'
                  protocol: '%PROTOCOL%'
                  requested_server_name: '%REQUESTED_SERVER_NAME%'
                  response_code: '%RESPONSE_CODE%'
                  response_code_details: '%RESPONSE_CODE_DETAILS%'
                  response_flags: '%RESPONSE_FLAGS%'
                  route_name: '%ROUTE_NAME%'
                  start_time: '%START_TIME%'
                  upstream_cluster: '%UPSTREAM_CLUSTER%'
                  upstream_host: '%UPSTREAM_HOST%'
                  upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                  upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                  user-agent: '%REQ(USER-AGENT)%'
                  x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                  x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                  x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                  x-request-id: '%REQ(X-REQUEST-ID)%'
              path: /dev/stdout
          address:
            socketAddress:
              address: 0.0.0.0
              portValue: 10080
          defaultFilterChain:
            filters:
            - name: envoy.filters.network.http_connection_manager
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                accessLog:
                - name: envoy.access_loggers.file
                  typedConfig:
                    '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                    logFormat:
                      jsonFormat:
                        :authority: '%REQ(:AUTHORITY)%'
                        bytes_received: '%BYTES_RECEIVED%'
                        bytes_sent: '%BYTES_SENT%'
                        connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                        downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                        downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                        duration: '%DURATION%'
                        method: '%REQ(:METHOD)%'
                        protocol: '%PROTOCOL%'
                        requested_server_name: '%REQUESTED_SERVER_NAME%'
                        response_code: '%RESPONSE_CODE%'
                        response_code_details: '%RESPONSE_CODE_DETAILS%'
                        response_flags: '%RESPONSE_FLAGS%'
                        route_name: '%ROUTE_NAME%'
                        start_time: '%START_TIME%'
                        upstream_cluster: '%UPSTREAM_CLUSTER%'
                        upstream_host: '%UPSTREAM_HOST%'
                        upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                        upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                        user-agent: '%REQ(USER-AGENT)%'
                        x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                        x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                        x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                        x-request-id: '%REQ(X-REQUEST-ID)%'
                    path: /dev/stdout
                commonHttpProtocolOptions:
                  headersWithUnderscoresAction: REJECT_REQUEST
                http2ProtocolOptions:
                  initialConnectionWindowSize: 1048576
                  initialStreamWindowSize: 65536
                  maxConcurrentStreams: 100
                httpFilters:
                - name: envoy.filters.http.jwt_authn
                  typedConfig:
                    '@type': type.googleapis.com/envoy.extensions.filters.http.jwt_authn.v3.JwtAuthentication
                    providers:
                      httproute/envoy-gateway-system/backend/rule/0/match/0/www_example_com/example:
                        forward: true
                        normalizePayloadInMetadata:
                          spaceDelimitedClaims:
                          - scope
                        payloadInMetadata: example
                        remoteJwks:
                          asyncFetch: {}
                          cacheDuration: 300s
                          httpUri:
                            cluster: raw_githubusercontent_com_443
                            timeout: 10s
                            uri: https://raw.githubusercontent.com/envoyproxy/gateway/main/examples/kubernetes/jwt/jwks.json
                    requirementMap:
                      httproute/envoy-gateway-system/backend/rule/0/match/0/www_example_com:
                        providerName: httproute/envoy-gateway-system/backend/rule/0/match/0/www_example_com/example
                - name: envoy.filters.http.router
                  typedConfig:
                    '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                    suppressEnvoyHeaders: true
                mergeSlashes: true
                normalizePath: true
                pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
                rds:
                  configSource:
                    ads: {}
                    resourceApiVersion: V3
                  routeConfigName: envoy-gateway-system/eg/http
                serverHeaderTransformation: PASS_THROUGH
                statPrefix: http-10080
                useRemoteAddress: true
            name: envoy-gateway-system/eg/http
          maxConnectionsToAcceptPerSocketEvent: 1
          name: envoy-gateway-system/eg/http
          perConnectionBufferLimitBytes: 32768

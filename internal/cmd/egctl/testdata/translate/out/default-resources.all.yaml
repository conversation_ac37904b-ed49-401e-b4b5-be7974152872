envoyProxyForGatewayClass:
  apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyProxy
  metadata:
    creationTimestamp: null
    name: default-envoy-proxy
    namespace: envoy-gateway-system
  spec:
    bootstrap:
      type: null
      value: |
        admin:
          access_log:
          - name: envoy.access_loggers.file
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
              path: /dev/null
          address:
            socket_address:
              address: 127.0.0.1
              port_value: 19000
        cluster_manager:
          local_cluster_name: local_cluster
        node:
          locality:
            zone: $(ENVOY_SERVICE_ZONE)
        layered_runtime:
          layers:
          - name: global_config
            static_layer:
              envoy.restart_features.use_eds_cache_for_ads: true
              re2.max_program_size.error_level: 4294967295
              re2.max_program_size.warn_level: 1000
        dynamic_resources:
          ads_config:
            api_type: DELTA_GRPC
            transport_api_version: V3
            grpc_services:
            - envoy_grpc:
                cluster_name: xds_cluster
            set_node_on_first_message_only: true
          lds_config:
            ads: {}
            resource_api_version: V3
          cds_config:
            ads: {}
            resource_api_version: V3
        static_resources:
          listeners:
          - name: envoy-gateway-proxy-stats-0.0.0.0-19001
            address:
              socket_address:
                address: '0.0.0.0'
                port_value: 19001
                protocol: TCP
            bypass_overload_manager: true
            filter_chains:
            - filters:
              - name: envoy.filters.network.http_connection_manager
                typed_config:
                  "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                  stat_prefix: eg-stats-http
                  normalize_path: true
                  route_config:
                    name: local_route
                    virtual_hosts:
                    - name: prometheus_stats
                      domains:
                      - "*"
                      routes:
                      - match:
                          path: /stats/prometheus
                          headers:
                          - name: ":method"
                            string_match:
                              exact: GET
                        route:
                          cluster: prometheus_stats
                  http_filters:
                  - name: envoy.filters.http.router
                    typed_config:
                      "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
          clusters:
          - name: prometheus_stats
            connect_timeout: 0.250s
            type: STATIC
            lb_policy: ROUND_ROBIN
            load_assignment:
              cluster_name: prometheus_stats
              endpoints:
              - lb_endpoints:
                - endpoint:
                    address:
                      socket_address:
                        address: 127.0.0.1
                        port_value: 19000
          - connect_timeout: 10s
            lb_policy: ROUND_ROBIN
            load_assignment:
              cluster_name: local_cluster
              endpoints:
              - lb_endpoints:
                - endpoint:
                    address:
                      socket_address:
                        address: 127.0.0.1
                        port_value: 10080
                  load_balancing_weight: 1
                load_balancing_weight: 1
                locality:
                  zone: $(ENVOY_SERVICE_ZONE)
            name: local_cluster
            type: STATIC
          - connect_timeout: 10s
            load_assignment:
              cluster_name: xds_cluster
              endpoints:
              - load_balancing_weight: 1
                lb_endpoints:
                - load_balancing_weight: 1
                  endpoint:
                    address:
                      socket_address:
                        address: envoy-gateway
                        port_value: 18000
            typed_extension_protocol_options:
              envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
                "@type": "type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions"
                explicit_http_config:
                  http2_protocol_options:
                    connection_keepalive:
                      interval: 30s
                      timeout: 5s
            name: xds_cluster
            type: STRICT_DNS
            transport_socket:
              name: envoy.transport_sockets.tls
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
                common_tls_context:
                  tls_params:
                    tls_maximum_protocol_version: TLSv1_3
                  tls_certificate_sds_secret_configs:
                  - name: xds_certificate
                    sds_config:
                      path_config_source:
                        path: /sds/xds-certificate.json
                      resource_api_version: V3
                  validation_context_sds_secret_config:
                    name: xds_trusted_ca
                    sds_config:
                      path_config_source:
                        path: /sds/xds-trusted-ca.json
                      resource_api_version: V3
        overload_manager:
          refresh_interval: 0.25s
          resource_monitors:
          - name: "envoy.resource_monitors.global_downstream_max_connections"
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.resource_monitors.downstream_connections.v3.DownstreamConnectionsConfig
              max_active_downstream_connections: 50000
    logging: {}
  status: {}
gatewayClass:
  apiVersion: gateway.networking.k8s.io/v1
  kind: GatewayClass
  metadata:
    creationTimestamp: null
    name: eg
  spec:
    controllerName: gateway.envoyproxy.io/gatewayclass-controller
    parametersRef:
      group: gateway.envoyproxy.io
      kind: EnvoyProxy
      name: default-envoy-proxy
      namespace: envoy-gateway-system
  status:
    conditions:
    - lastTransitionTime: null
      message: Valid GatewayClass
      reason: Accepted
      status: "True"
      type: Accepted
gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: eg
    namespace: default
  spec:
    gatewayClassName: eg
    listeners:
    - allowedRoutes:
        namespaces:
          from: Same
      name: tcp
      port: 1234
      protocol: TCP
    - allowedRoutes:
        namespaces:
          from: Same
      name: udp
      port: 1234
      protocol: UDP
    - allowedRoutes:
        namespaces:
          from: Same
      hostname: foo.com
      name: tls-passthrough
      port: 8443
      protocol: TLS
      tls:
        mode: Passthrough
    - allowedRoutes:
        kinds:
        - group: gateway.networking.k8s.io
          kind: HTTPRoute
        namespaces:
          from: Same
      name: http
      port: 80
      protocol: HTTP
    - allowedRoutes:
        kinds:
        - group: gateway.networking.k8s.io
          kind: GRPCRoute
        namespaces:
          from: Same
      name: grpc
      port: 8080
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: tcp
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: TCPRoute
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: udp
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: UDPRoute
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: tls-passthrough
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: TLSRoute
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: grpc
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
grpcRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: GRPCRoute
  metadata:
    creationTimestamp: null
    name: backend
    namespace: default
  spec:
    hostnames:
    - www.grpc-example.com
    parentRefs:
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: eg
      sectionName: grpc
    rules:
    - backendRefs:
      - group: ""
        kind: Service
        name: providedBackend
        port: 9000
        weight: 1
      matches:
      - method:
          method: DoThing
          service: com.example.Things
          type: Exact
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: eg
        sectionName: grpc
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: backend
    namespace: default
  spec:
    hostnames:
    - www.example.com
    parentRefs:
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: eg
    rules:
    - backendRefs:
      - group: ""
        kind: Service
        name: providedBackend
        port: 8000
        weight: 1
      matches:
      - path:
          type: PathPrefix
          value: /
      name: rule-1
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: eg
tcpRoutes:
- apiVersion: gateway.networking.k8s.io/v1alpha2
  kind: TCPRoute
  metadata:
    creationTimestamp: null
    name: backend
    namespace: default
  spec:
    parentRefs:
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: eg
      sectionName: tcp
    rules:
    - backendRefs:
      - group: ""
        kind: Service
        name: backend
        port: 3000
        weight: 1
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: eg
        sectionName: tcp
tlsRoutes:
- apiVersion: gateway.networking.k8s.io/v1alpha2
  kind: TLSRoute
  metadata:
    creationTimestamp: null
    name: backend
    namespace: default
  spec:
    parentRefs:
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: eg
      sectionName: tls-passthrough
    rules:
    - backendRefs:
      - group: ""
        kind: Service
        name: backend
        port: 3000
        weight: 1
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: eg
        sectionName: tls-passthrough
udpRoutes:
- apiVersion: gateway.networking.k8s.io/v1alpha2
  kind: UDPRoute
  metadata:
    creationTimestamp: null
    name: backend
    namespace: default
  spec:
    parentRefs:
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: eg
      sectionName: udp
    rules:
    - backendRefs:
      - group: ""
        kind: Service
        name: backend
        port: 3000
        weight: 1
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: eg
        sectionName: udp
xds:
  default/eg:
    configs:
    - '@type': type.googleapis.com/envoy.admin.v3.BootstrapConfigDump
      bootstrap:
        admin:
          accessLog:
          - name: envoy.access_loggers.file
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
              path: /dev/null
          address:
            socketAddress:
              address: 127.0.0.1
              portValue: 19000
        clusterManager:
          localClusterName: local_cluster
        dynamicResources:
          adsConfig:
            apiType: DELTA_GRPC
            grpcServices:
            - envoyGrpc:
                clusterName: xds_cluster
            setNodeOnFirstMessageOnly: true
            transportApiVersion: V3
          cdsConfig:
            ads: {}
            resourceApiVersion: V3
          ldsConfig:
            ads: {}
            resourceApiVersion: V3
        layeredRuntime:
          layers:
          - name: global_config
            staticLayer:
              envoy.restart_features.use_eds_cache_for_ads: true
              re2.max_program_size.error_level: 4294967295
              re2.max_program_size.warn_level: 1000
        node:
          locality:
            zone: $(ENVOY_SERVICE_ZONE)
        overloadManager:
          refreshInterval: 0.250s
          resourceMonitors:
          - name: envoy.resource_monitors.global_downstream_max_connections
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.resource_monitors.downstream_connections.v3.DownstreamConnectionsConfig
              maxActiveDownstreamConnections: "50000"
        staticResources:
          clusters:
          - connectTimeout: 0.250s
            loadAssignment:
              clusterName: prometheus_stats
              endpoints:
              - lbEndpoints:
                - endpoint:
                    address:
                      socketAddress:
                        address: 127.0.0.1
                        portValue: 19000
            name: prometheus_stats
            type: STATIC
          - connectTimeout: 10s
            loadAssignment:
              clusterName: local_cluster
              endpoints:
              - lbEndpoints:
                - endpoint:
                    address:
                      socketAddress:
                        address: 127.0.0.1
                        portValue: 10080
                  loadBalancingWeight: 1
                loadBalancingWeight: 1
                locality:
                  zone: $(ENVOY_SERVICE_ZONE)
            name: local_cluster
            type: STATIC
          - connectTimeout: 10s
            loadAssignment:
              clusterName: xds_cluster
              endpoints:
              - lbEndpoints:
                - endpoint:
                    address:
                      socketAddress:
                        address: envoy-gateway
                        portValue: 18000
                  loadBalancingWeight: 1
                loadBalancingWeight: 1
            name: xds_cluster
            transportSocket:
              name: envoy.transport_sockets.tls
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
                commonTlsContext:
                  tlsCertificateSdsSecretConfigs:
                  - name: xds_certificate
                    sdsConfig:
                      pathConfigSource:
                        path: /sds/xds-certificate.json
                      resourceApiVersion: V3
                  tlsParams:
                    tlsMaximumProtocolVersion: TLSv1_3
                  validationContextSdsSecretConfig:
                    name: xds_trusted_ca
                    sdsConfig:
                      pathConfigSource:
                        path: /sds/xds-trusted-ca.json
                      resourceApiVersion: V3
            type: STRICT_DNS
            typedExtensionProtocolOptions:
              envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
                '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
                explicitHttpConfig:
                  http2ProtocolOptions:
                    connectionKeepalive:
                      interval: 30s
                      timeout: 5s
          listeners:
          - address:
              socketAddress:
                address: 0.0.0.0
                portValue: 19001
            bypassOverloadManager: true
            filterChains:
            - filters:
              - name: envoy.filters.network.http_connection_manager
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                  httpFilters:
                  - name: envoy.filters.http.router
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                  normalizePath: true
                  routeConfig:
                    name: local_route
                    virtualHosts:
                    - domains:
                      - '*'
                      name: prometheus_stats
                      routes:
                      - match:
                          headers:
                          - name: :method
                            stringMatch:
                              exact: GET
                          path: /stats/prometheus
                        route:
                          cluster: prometheus_stats
                  statPrefix: eg-stats-http
            name: envoy-gateway-proxy-stats-0.0.0.0-19001
    - '@type': type.googleapis.com/envoy.admin.v3.EndpointsConfigDump
      dynamicEndpointConfigs:
      - endpointConfig:
          '@type': type.googleapis.com/envoy.config.endpoint.v3.ClusterLoadAssignment
          clusterName: httproute/default/backend/rule/0
          endpoints:
          - lbEndpoints:
            - endpoint:
                address:
                  socketAddress:
                    address: *******
                    portValue: 8000
              loadBalancingWeight: 1
            loadBalancingWeight: 1
            locality:
              region: httproute/default/backend/rule/0/backend/0
            metadata:
              filterMetadata:
                envoy-gateway:
                  resources:
                  - kind: Service
                    name: providedBackend
                    namespace: default
                    sectionName: "8000"
      - endpointConfig:
          '@type': type.googleapis.com/envoy.config.endpoint.v3.ClusterLoadAssignment
          clusterName: grpcroute/default/backend/rule/0
          endpoints:
          - lbEndpoints:
            - endpoint:
                address:
                  socketAddress:
                    address: *******
                    portValue: 9000
              loadBalancingWeight: 1
            loadBalancingWeight: 1
            locality:
              region: grpcroute/default/backend/rule/0/backend/0
            metadata:
              filterMetadata:
                envoy-gateway:
                  resources:
                  - kind: Service
                    name: providedBackend
                    namespace: default
                    sectionName: "9000"
      - endpointConfig:
          '@type': type.googleapis.com/envoy.config.endpoint.v3.ClusterLoadAssignment
          clusterName: tcproute/default/backend/rule/-1
          endpoints:
          - lbEndpoints:
            - endpoint:
                address:
                  socketAddress:
                    address: *******
                    portValue: 3000
              loadBalancingWeight: 1
            loadBalancingWeight: 1
            locality:
              region: tcproute/default/backend/rule/-1/backend/0
            metadata:
              filterMetadata:
                envoy-gateway:
                  resources:
                  - kind: Service
                    name: backend
                    namespace: default
                    sectionName: "3000"
      - endpointConfig:
          '@type': type.googleapis.com/envoy.config.endpoint.v3.ClusterLoadAssignment
          clusterName: tlsroute/default/backend/rule/-1
          endpoints:
          - lbEndpoints:
            - endpoint:
                address:
                  socketAddress:
                    address: *******
                    portValue: 3000
              loadBalancingWeight: 1
            loadBalancingWeight: 1
            locality:
              region: tlsroute/default/backend/rule/-1/backend/0
            metadata:
              filterMetadata:
                envoy-gateway:
                  resources:
                  - kind: Service
                    name: backend
                    namespace: default
                    sectionName: "3000"
      - endpointConfig:
          '@type': type.googleapis.com/envoy.config.endpoint.v3.ClusterLoadAssignment
          clusterName: udproute/default/backend/rule/-1
          endpoints:
          - lbEndpoints:
            - endpoint:
                address:
                  socketAddress:
                    address: *******
                    portValue: 3000
              loadBalancingWeight: 1
            loadBalancingWeight: 1
            locality:
              region: udproute/default/backend/rule/-1/backend/0
            metadata:
              filterMetadata:
                envoy-gateway:
                  resources:
                  - kind: Service
                    name: backend
                    namespace: default
                    sectionName: "3000"
    - '@type': type.googleapis.com/envoy.admin.v3.ClustersConfigDump
      dynamicActiveClusters:
      - cluster:
          '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
          circuitBreakers:
            thresholds:
            - maxRetries: 1024
          commonLbConfig:
            localityWeightedLbConfig: {}
          connectTimeout: 10s
          dnsLookupFamily: V4_PREFERRED
          edsClusterConfig:
            edsConfig:
              ads: {}
              resourceApiVersion: V3
            serviceName: httproute/default/backend/rule/0
          ignoreHealthOnHostRemoval: true
          lbPolicy: LEAST_REQUEST
          metadata:
            filterMetadata:
              envoy-gateway:
                resources:
                - kind: HTTPRoute
                  name: backend
                  namespace: default
                  sectionName: rule-1
          name: httproute/default/backend/rule/0
          perConnectionBufferLimitBytes: 32768
          type: EDS
      - cluster:
          '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
          circuitBreakers:
            thresholds:
            - maxRetries: 1024
          commonLbConfig:
            localityWeightedLbConfig: {}
          connectTimeout: 10s
          dnsLookupFamily: V4_PREFERRED
          edsClusterConfig:
            edsConfig:
              ads: {}
              resourceApiVersion: V3
            serviceName: grpcroute/default/backend/rule/0
          ignoreHealthOnHostRemoval: true
          lbPolicy: LEAST_REQUEST
          metadata:
            filterMetadata:
              envoy-gateway:
                resources:
                - kind: GRPCRoute
                  name: backend
                  namespace: default
          name: grpcroute/default/backend/rule/0
          perConnectionBufferLimitBytes: 32768
          type: EDS
          typedExtensionProtocolOptions:
            envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
              '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
              explicitHttpConfig:
                http2ProtocolOptions:
                  initialConnectionWindowSize: 1048576
                  initialStreamWindowSize: 65536
      - cluster:
          '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
          circuitBreakers:
            thresholds:
            - maxRetries: 1024
          commonLbConfig:
            localityWeightedLbConfig: {}
          connectTimeout: 10s
          dnsLookupFamily: V4_PREFERRED
          edsClusterConfig:
            edsConfig:
              ads: {}
              resourceApiVersion: V3
            serviceName: tcproute/default/backend/rule/-1
          ignoreHealthOnHostRemoval: true
          lbPolicy: LEAST_REQUEST
          metadata:
            filterMetadata:
              envoy-gateway:
                resources:
                - kind: TCPRoute
                  name: backend
                  namespace: default
          name: tcproute/default/backend/rule/-1
          perConnectionBufferLimitBytes: 32768
          type: EDS
      - cluster:
          '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
          circuitBreakers:
            thresholds:
            - maxRetries: 1024
          commonLbConfig:
            localityWeightedLbConfig: {}
          connectTimeout: 10s
          dnsLookupFamily: V4_PREFERRED
          edsClusterConfig:
            edsConfig:
              ads: {}
              resourceApiVersion: V3
            serviceName: tlsroute/default/backend/rule/-1
          ignoreHealthOnHostRemoval: true
          lbPolicy: LEAST_REQUEST
          metadata:
            filterMetadata:
              envoy-gateway:
                resources:
                - kind: TLSRoute
                  name: backend
                  namespace: default
          name: tlsroute/default/backend/rule/-1
          perConnectionBufferLimitBytes: 32768
          type: EDS
      - cluster:
          '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
          circuitBreakers:
            thresholds:
            - maxRetries: 1024
          commonLbConfig:
            localityWeightedLbConfig: {}
          connectTimeout: 10s
          dnsLookupFamily: V4_PREFERRED
          edsClusterConfig:
            edsConfig:
              ads: {}
              resourceApiVersion: V3
            serviceName: udproute/default/backend/rule/-1
          ignoreHealthOnHostRemoval: true
          lbPolicy: LEAST_REQUEST
          metadata:
            filterMetadata:
              envoy-gateway:
                resources:
                - kind: UDPRoute
                  name: backend
                  namespace: default
          name: udproute/default/backend/rule/-1
          perConnectionBufferLimitBytes: 32768
          type: EDS
    - '@type': type.googleapis.com/envoy.admin.v3.ListenersConfigDump
      dynamicListeners:
      - activeState:
          listener:
            '@type': type.googleapis.com/envoy.config.listener.v3.Listener
            address:
              socketAddress:
                address: 0.0.0.0
                portValue: 19003
            bypassOverloadManager: true
            filterChains:
            - filters:
              - name: envoy.filters.network.http_connection_manager
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                  httpFilters:
                  - name: envoy.filters.http.health_check
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.health_check.v3.HealthCheck
                      headers:
                      - name: :path
                        stringMatch:
                          exact: /ready
                      passThroughMode: false
                  - name: envoy.filters.http.router
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                      suppressEnvoyHeaders: true
                  routeConfig:
                    name: ready_route
                    virtualHosts:
                    - domains:
                      - '*'
                      name: ready_route
                      routes:
                      - directResponse:
                          status: 500
                        match:
                          prefix: /
                  statPrefix: eg-ready-http
            name: envoy-gateway-proxy-ready-0.0.0.0-19003
      - activeState:
          listener:
            '@type': type.googleapis.com/envoy.config.listener.v3.Listener
            accessLog:
            - filter:
                responseFlagFilter:
                  flags:
                  - NR
              name: envoy.access_loggers.file
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                logFormat:
                  jsonFormat:
                    :authority: '%REQ(:AUTHORITY)%'
                    bytes_received: '%BYTES_RECEIVED%'
                    bytes_sent: '%BYTES_SENT%'
                    connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                    downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                    downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                    duration: '%DURATION%'
                    method: '%REQ(:METHOD)%'
                    protocol: '%PROTOCOL%'
                    requested_server_name: '%REQUESTED_SERVER_NAME%'
                    response_code: '%RESPONSE_CODE%'
                    response_code_details: '%RESPONSE_CODE_DETAILS%'
                    response_flags: '%RESPONSE_FLAGS%'
                    route_name: '%ROUTE_NAME%'
                    start_time: '%START_TIME%'
                    upstream_cluster: '%UPSTREAM_CLUSTER%'
                    upstream_host: '%UPSTREAM_HOST%'
                    upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                    upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                    user-agent: '%REQ(USER-AGENT)%'
                    x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                    x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                    x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                    x-request-id: '%REQ(X-REQUEST-ID)%'
                path: /dev/stdout
            address:
              socketAddress:
                address: 0.0.0.0
                portValue: 10080
            defaultFilterChain:
              filters:
              - name: envoy.filters.network.http_connection_manager
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                  accessLog:
                  - name: envoy.access_loggers.file
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                      logFormat:
                        jsonFormat:
                          :authority: '%REQ(:AUTHORITY)%'
                          bytes_received: '%BYTES_RECEIVED%'
                          bytes_sent: '%BYTES_SENT%'
                          connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                          downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                          downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                          duration: '%DURATION%'
                          method: '%REQ(:METHOD)%'
                          protocol: '%PROTOCOL%'
                          requested_server_name: '%REQUESTED_SERVER_NAME%'
                          response_code: '%RESPONSE_CODE%'
                          response_code_details: '%RESPONSE_CODE_DETAILS%'
                          response_flags: '%RESPONSE_FLAGS%'
                          route_name: '%ROUTE_NAME%'
                          start_time: '%START_TIME%'
                          upstream_cluster: '%UPSTREAM_CLUSTER%'
                          upstream_host: '%UPSTREAM_HOST%'
                          upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                          upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                          user-agent: '%REQ(USER-AGENT)%'
                          x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                          x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                          x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                          x-request-id: '%REQ(X-REQUEST-ID)%'
                      path: /dev/stdout
                  commonHttpProtocolOptions:
                    headersWithUnderscoresAction: REJECT_REQUEST
                  http2ProtocolOptions:
                    initialConnectionWindowSize: 1048576
                    initialStreamWindowSize: 65536
                    maxConcurrentStreams: 100
                  httpFilters:
                  - name: envoy.filters.http.router
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                      suppressEnvoyHeaders: true
                  mergeSlashes: true
                  normalizePath: true
                  pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
                  rds:
                    configSource:
                      ads: {}
                      resourceApiVersion: V3
                    routeConfigName: default/eg/http
                  serverHeaderTransformation: PASS_THROUGH
                  statPrefix: http-10080
                  useRemoteAddress: true
              name: default/eg/http
            maxConnectionsToAcceptPerSocketEvent: 1
            name: default/eg/http
            perConnectionBufferLimitBytes: 32768
      - activeState:
          listener:
            '@type': type.googleapis.com/envoy.config.listener.v3.Listener
            accessLog:
            - filter:
                responseFlagFilter:
                  flags:
                  - NR
              name: envoy.access_loggers.file
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                logFormat:
                  jsonFormat:
                    :authority: '%REQ(:AUTHORITY)%'
                    bytes_received: '%BYTES_RECEIVED%'
                    bytes_sent: '%BYTES_SENT%'
                    connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                    downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                    downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                    duration: '%DURATION%'
                    method: '%REQ(:METHOD)%'
                    protocol: '%PROTOCOL%'
                    requested_server_name: '%REQUESTED_SERVER_NAME%'
                    response_code: '%RESPONSE_CODE%'
                    response_code_details: '%RESPONSE_CODE_DETAILS%'
                    response_flags: '%RESPONSE_FLAGS%'
                    route_name: '%ROUTE_NAME%'
                    start_time: '%START_TIME%'
                    upstream_cluster: '%UPSTREAM_CLUSTER%'
                    upstream_host: '%UPSTREAM_HOST%'
                    upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                    upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                    user-agent: '%REQ(USER-AGENT)%'
                    x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                    x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                    x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                    x-request-id: '%REQ(X-REQUEST-ID)%'
                path: /dev/stdout
            address:
              socketAddress:
                address: 0.0.0.0
                portValue: 8080
            defaultFilterChain:
              filters:
              - name: envoy.filters.network.http_connection_manager
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                  accessLog:
                  - name: envoy.access_loggers.file
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                      logFormat:
                        jsonFormat:
                          :authority: '%REQ(:AUTHORITY)%'
                          bytes_received: '%BYTES_RECEIVED%'
                          bytes_sent: '%BYTES_SENT%'
                          connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                          downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                          downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                          duration: '%DURATION%'
                          method: '%REQ(:METHOD)%'
                          protocol: '%PROTOCOL%'
                          requested_server_name: '%REQUESTED_SERVER_NAME%'
                          response_code: '%RESPONSE_CODE%'
                          response_code_details: '%RESPONSE_CODE_DETAILS%'
                          response_flags: '%RESPONSE_FLAGS%'
                          route_name: '%ROUTE_NAME%'
                          start_time: '%START_TIME%'
                          upstream_cluster: '%UPSTREAM_CLUSTER%'
                          upstream_host: '%UPSTREAM_HOST%'
                          upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                          upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                          user-agent: '%REQ(USER-AGENT)%'
                          x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                          x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                          x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                          x-request-id: '%REQ(X-REQUEST-ID)%'
                      path: /dev/stdout
                  commonHttpProtocolOptions:
                    headersWithUnderscoresAction: REJECT_REQUEST
                  http2ProtocolOptions:
                    initialConnectionWindowSize: 1048576
                    initialStreamWindowSize: 65536
                    maxConcurrentStreams: 100
                  httpFilters:
                  - name: envoy.filters.http.grpc_web
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.grpc_web.v3.GrpcWeb
                  - name: envoy.filters.http.grpc_stats
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.grpc_stats.v3.FilterConfig
                      emitFilterState: true
                      statsForAllMethods: true
                  - name: envoy.filters.http.router
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                      suppressEnvoyHeaders: true
                  mergeSlashes: true
                  normalizePath: true
                  pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
                  rds:
                    configSource:
                      ads: {}
                      resourceApiVersion: V3
                    routeConfigName: default/eg/grpc
                  serverHeaderTransformation: PASS_THROUGH
                  statPrefix: http-8080
                  useRemoteAddress: true
              name: default/eg/grpc
            maxConnectionsToAcceptPerSocketEvent: 1
            name: default/eg/grpc
            perConnectionBufferLimitBytes: 32768
      - activeState:
          listener:
            '@type': type.googleapis.com/envoy.config.listener.v3.Listener
            accessLog:
            - filter:
                responseFlagFilter:
                  flags:
                  - NR
              name: envoy.access_loggers.file
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                logFormat:
                  jsonFormat:
                    :authority: '%REQ(:AUTHORITY)%'
                    bytes_received: '%BYTES_RECEIVED%'
                    bytes_sent: '%BYTES_SENT%'
                    connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                    downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                    downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                    duration: '%DURATION%'
                    method: '%REQ(:METHOD)%'
                    protocol: '%PROTOCOL%'
                    requested_server_name: '%REQUESTED_SERVER_NAME%'
                    response_code: '%RESPONSE_CODE%'
                    response_code_details: '%RESPONSE_CODE_DETAILS%'
                    response_flags: '%RESPONSE_FLAGS%'
                    route_name: '%ROUTE_NAME%'
                    start_time: '%START_TIME%'
                    upstream_cluster: '%UPSTREAM_CLUSTER%'
                    upstream_host: '%UPSTREAM_HOST%'
                    upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                    upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                    user-agent: '%REQ(USER-AGENT)%'
                    x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                    x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                    x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                    x-request-id: '%REQ(X-REQUEST-ID)%'
                path: /dev/stdout
            address:
              socketAddress:
                address: 0.0.0.0
                portValue: 1234
            filterChains:
            - filters:
              - name: envoy.filters.network.tcp_proxy
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy
                  accessLog:
                  - name: envoy.access_loggers.file
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                      logFormat:
                        jsonFormat:
                          :authority: '%REQ(:AUTHORITY)%'
                          bytes_received: '%BYTES_RECEIVED%'
                          bytes_sent: '%BYTES_SENT%'
                          connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                          downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                          downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                          duration: '%DURATION%'
                          method: '%REQ(:METHOD)%'
                          protocol: '%PROTOCOL%'
                          requested_server_name: '%REQUESTED_SERVER_NAME%'
                          response_code: '%RESPONSE_CODE%'
                          response_code_details: '%RESPONSE_CODE_DETAILS%'
                          response_flags: '%RESPONSE_FLAGS%'
                          route_name: '%ROUTE_NAME%'
                          start_time: '%START_TIME%'
                          upstream_cluster: '%UPSTREAM_CLUSTER%'
                          upstream_host: '%UPSTREAM_HOST%'
                          upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                          upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                          user-agent: '%REQ(USER-AGENT)%'
                          x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                          x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                          x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                          x-request-id: '%REQ(X-REQUEST-ID)%'
                      path: /dev/stdout
                  cluster: tcproute/default/backend/rule/-1
                  statPrefix: tcp-1234
              name: tcproute/default/backend
            maxConnectionsToAcceptPerSocketEvent: 1
            name: default/eg/tcp
            perConnectionBufferLimitBytes: 32768
      - activeState:
          listener:
            '@type': type.googleapis.com/envoy.config.listener.v3.Listener
            accessLog:
            - filter:
                responseFlagFilter:
                  flags:
                  - NR
              name: envoy.access_loggers.file
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                logFormat:
                  jsonFormat:
                    :authority: '%REQ(:AUTHORITY)%'
                    bytes_received: '%BYTES_RECEIVED%'
                    bytes_sent: '%BYTES_SENT%'
                    connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                    downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                    downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                    duration: '%DURATION%'
                    method: '%REQ(:METHOD)%'
                    protocol: '%PROTOCOL%'
                    requested_server_name: '%REQUESTED_SERVER_NAME%'
                    response_code: '%RESPONSE_CODE%'
                    response_code_details: '%RESPONSE_CODE_DETAILS%'
                    response_flags: '%RESPONSE_FLAGS%'
                    route_name: '%ROUTE_NAME%'
                    start_time: '%START_TIME%'
                    upstream_cluster: '%UPSTREAM_CLUSTER%'
                    upstream_host: '%UPSTREAM_HOST%'
                    upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                    upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                    user-agent: '%REQ(USER-AGENT)%'
                    x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                    x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                    x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                    x-request-id: '%REQ(X-REQUEST-ID)%'
                path: /dev/stdout
            address:
              socketAddress:
                address: 0.0.0.0
                portValue: 8443
            filterChains:
            - filterChainMatch:
                serverNames:
                - foo.com
              filters:
              - name: envoy.filters.network.tcp_proxy
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy
                  accessLog:
                  - name: envoy.access_loggers.file
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                      logFormat:
                        jsonFormat:
                          :authority: '%REQ(:AUTHORITY)%'
                          bytes_received: '%BYTES_RECEIVED%'
                          bytes_sent: '%BYTES_SENT%'
                          connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                          downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                          downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                          duration: '%DURATION%'
                          method: '%REQ(:METHOD)%'
                          protocol: '%PROTOCOL%'
                          requested_server_name: '%REQUESTED_SERVER_NAME%'
                          response_code: '%RESPONSE_CODE%'
                          response_code_details: '%RESPONSE_CODE_DETAILS%'
                          response_flags: '%RESPONSE_FLAGS%'
                          route_name: '%ROUTE_NAME%'
                          start_time: '%START_TIME%'
                          upstream_cluster: '%UPSTREAM_CLUSTER%'
                          upstream_host: '%UPSTREAM_HOST%'
                          upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                          upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                          user-agent: '%REQ(USER-AGENT)%'
                          x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                          x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                          x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                          x-request-id: '%REQ(X-REQUEST-ID)%'
                      path: /dev/stdout
                  cluster: tlsroute/default/backend/rule/-1
                  statPrefix: tls-passthrough-8443
              name: tlsroute/default/backend
            listenerFilters:
            - name: envoy.filters.listener.tls_inspector
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.filters.listener.tls_inspector.v3.TlsInspector
            maxConnectionsToAcceptPerSocketEvent: 1
            name: default/eg/tls-passthrough
            perConnectionBufferLimitBytes: 32768
      - activeState:
          listener:
            '@type': type.googleapis.com/envoy.config.listener.v3.Listener
            accessLog:
            - filter:
                responseFlagFilter:
                  flags:
                  - NR
              name: envoy.access_loggers.file
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                logFormat:
                  jsonFormat:
                    :authority: '%REQ(:AUTHORITY)%'
                    bytes_received: '%BYTES_RECEIVED%'
                    bytes_sent: '%BYTES_SENT%'
                    connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                    downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                    downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                    duration: '%DURATION%'
                    method: '%REQ(:METHOD)%'
                    protocol: '%PROTOCOL%'
                    requested_server_name: '%REQUESTED_SERVER_NAME%'
                    response_code: '%RESPONSE_CODE%'
                    response_code_details: '%RESPONSE_CODE_DETAILS%'
                    response_flags: '%RESPONSE_FLAGS%'
                    route_name: '%ROUTE_NAME%'
                    start_time: '%START_TIME%'
                    upstream_cluster: '%UPSTREAM_CLUSTER%'
                    upstream_host: '%UPSTREAM_HOST%'
                    upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                    upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                    user-agent: '%REQ(USER-AGENT)%'
                    x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                    x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                    x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                    x-request-id: '%REQ(X-REQUEST-ID)%'
                path: /dev/stdout
            address:
              socketAddress:
                address: 0.0.0.0
                portValue: 1234
                protocol: UDP
            listenerFilters:
            - name: envoy.filters.udp_listener.udp_proxy
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.filters.udp.udp_proxy.v3.UdpProxyConfig
                accessLog:
                - name: envoy.access_loggers.file
                  typedConfig:
                    '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                    logFormat:
                      jsonFormat:
                        :authority: '%REQ(:AUTHORITY)%'
                        bytes_received: '%BYTES_RECEIVED%'
                        bytes_sent: '%BYTES_SENT%'
                        connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                        downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                        downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                        duration: '%DURATION%'
                        method: '%REQ(:METHOD)%'
                        protocol: '%PROTOCOL%'
                        requested_server_name: '%REQUESTED_SERVER_NAME%'
                        response_code: '%RESPONSE_CODE%'
                        response_code_details: '%RESPONSE_CODE_DETAILS%'
                        response_flags: '%RESPONSE_FLAGS%'
                        route_name: '%ROUTE_NAME%'
                        start_time: '%START_TIME%'
                        upstream_cluster: '%UPSTREAM_CLUSTER%'
                        upstream_host: '%UPSTREAM_HOST%'
                        upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                        upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                        user-agent: '%REQ(USER-AGENT)%'
                        x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                        x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                        x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                        x-request-id: '%REQ(X-REQUEST-ID)%'
                    path: /dev/stdout
                matcher:
                  onNoMatch:
                    action:
                      name: route
                      typedConfig:
                        '@type': type.googleapis.com/envoy.extensions.filters.udp.udp_proxy.v3.Route
                        cluster: udproute/default/backend/rule/-1
                statPrefix: service
            name: default/eg/udp
    - '@type': type.googleapis.com/envoy.admin.v3.RoutesConfigDump
      dynamicRouteConfigs:
      - routeConfig:
          '@type': type.googleapis.com/envoy.config.route.v3.RouteConfiguration
          ignorePortInHostMatching: true
          name: default/eg/http
          virtualHosts:
          - domains:
            - www.example.com
            metadata:
              filterMetadata:
                envoy-gateway:
                  resources:
                  - kind: Gateway
                    name: eg
                    namespace: default
                    sectionName: http
            name: default/eg/http/www_example_com
            routes:
            - match:
                prefix: /
              metadata:
                filterMetadata:
                  envoy-gateway:
                    resources:
                    - kind: HTTPRoute
                      name: backend
                      namespace: default
                      sectionName: rule-1
              name: httproute/default/backend/rule/0/match/0/www_example_com
              route:
                cluster: httproute/default/backend/rule/0
                upgradeConfigs:
                - upgradeType: websocket
      - routeConfig:
          '@type': type.googleapis.com/envoy.config.route.v3.RouteConfiguration
          ignorePortInHostMatching: true
          name: default/eg/grpc
          virtualHosts:
          - domains:
            - www.grpc-example.com
            metadata:
              filterMetadata:
                envoy-gateway:
                  resources:
                  - kind: Gateway
                    name: eg
                    namespace: default
                    sectionName: grpc
            name: default/eg/grpc/www_grpc-example_com
            routes:
            - match:
                path: /com.example.Things/DoThing
              metadata:
                filterMetadata:
                  envoy-gateway:
                    resources:
                    - kind: GRPCRoute
                      name: backend
                      namespace: default
              name: grpcroute/default/backend/rule/0/match/0/www_grpc-example_com
              route:
                cluster: grpcroute/default/backend/rule/0

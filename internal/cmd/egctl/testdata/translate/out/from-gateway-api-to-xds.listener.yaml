xds:
  default/eg:
    '@type': type.googleapis.com/envoy.admin.v3.ListenersConfigDump
    dynamicListeners:
    - activeState:
        listener:
          '@type': type.googleapis.com/envoy.config.listener.v3.Listener
          address:
            socketAddress:
              address: 0.0.0.0
              portValue: 19003
          bypassOverloadManager: true
          filterChains:
          - filters:
            - name: envoy.filters.network.http_connection_manager
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                httpFilters:
                - name: envoy.filters.http.health_check
                  typedConfig:
                    '@type': type.googleapis.com/envoy.extensions.filters.http.health_check.v3.HealthCheck
                    headers:
                    - name: :path
                      stringMatch:
                        exact: /ready
                    passThroughMode: false
                - name: envoy.filters.http.router
                  typedConfig:
                    '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                    suppressEnvoyHeaders: true
                routeConfig:
                  name: ready_route
                  virtualHosts:
                  - domains:
                    - '*'
                    name: ready_route
                    routes:
                    - directResponse:
                        status: 500
                      match:
                        prefix: /
                statPrefix: eg-ready-http
          name: envoy-gateway-proxy-ready-0.0.0.0-19003
    - activeState:
        listener:
          '@type': type.googleapis.com/envoy.config.listener.v3.Listener
          accessLog:
          - filter:
              responseFlagFilter:
                flags:
                - NR
            name: envoy.access_loggers.file
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
              logFormat:
                jsonFormat:
                  :authority: '%REQ(:AUTHORITY)%'
                  bytes_received: '%BYTES_RECEIVED%'
                  bytes_sent: '%BYTES_SENT%'
                  connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                  downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                  downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                  duration: '%DURATION%'
                  method: '%REQ(:METHOD)%'
                  protocol: '%PROTOCOL%'
                  requested_server_name: '%REQUESTED_SERVER_NAME%'
                  response_code: '%RESPONSE_CODE%'
                  response_code_details: '%RESPONSE_CODE_DETAILS%'
                  response_flags: '%RESPONSE_FLAGS%'
                  route_name: '%ROUTE_NAME%'
                  start_time: '%START_TIME%'
                  upstream_cluster: '%UPSTREAM_CLUSTER%'
                  upstream_host: '%UPSTREAM_HOST%'
                  upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                  upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                  user-agent: '%REQ(USER-AGENT)%'
                  x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                  x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                  x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                  x-request-id: '%REQ(X-REQUEST-ID)%'
              path: /dev/stdout
          address:
            socketAddress:
              address: 0.0.0.0
              portValue: 10080
          defaultFilterChain:
            filters:
            - name: envoy.filters.network.http_connection_manager
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                accessLog:
                - name: envoy.access_loggers.file
                  typedConfig:
                    '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                    logFormat:
                      jsonFormat:
                        :authority: '%REQ(:AUTHORITY)%'
                        bytes_received: '%BYTES_RECEIVED%'
                        bytes_sent: '%BYTES_SENT%'
                        connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                        downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                        downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                        duration: '%DURATION%'
                        method: '%REQ(:METHOD)%'
                        protocol: '%PROTOCOL%'
                        requested_server_name: '%REQUESTED_SERVER_NAME%'
                        response_code: '%RESPONSE_CODE%'
                        response_code_details: '%RESPONSE_CODE_DETAILS%'
                        response_flags: '%RESPONSE_FLAGS%'
                        route_name: '%ROUTE_NAME%'
                        start_time: '%START_TIME%'
                        upstream_cluster: '%UPSTREAM_CLUSTER%'
                        upstream_host: '%UPSTREAM_HOST%'
                        upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                        upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                        user-agent: '%REQ(USER-AGENT)%'
                        x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                        x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                        x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                        x-request-id: '%REQ(X-REQUEST-ID)%'
                    path: /dev/stdout
                commonHttpProtocolOptions:
                  headersWithUnderscoresAction: REJECT_REQUEST
                http2ProtocolOptions:
                  initialConnectionWindowSize: 1048576
                  initialStreamWindowSize: 65536
                  maxConcurrentStreams: 100
                httpFilters:
                - name: envoy.filters.http.router
                  typedConfig:
                    '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                    suppressEnvoyHeaders: true
                mergeSlashes: true
                normalizePath: true
                pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
                rds:
                  configSource:
                    ads: {}
                    resourceApiVersion: V3
                  routeConfigName: default/eg/http
                serverHeaderTransformation: PASS_THROUGH
                statPrefix: http-10080
                useRemoteAddress: true
            name: default/eg/http
          maxConnectionsToAcceptPerSocketEvent: 1
          name: default/eg/http
          perConnectionBufferLimitBytes: 32768
    - activeState:
        listener:
          '@type': type.googleapis.com/envoy.config.listener.v3.Listener
          accessLog:
          - filter:
              responseFlagFilter:
                flags:
                - NR
            name: envoy.access_loggers.file
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
              logFormat:
                jsonFormat:
                  :authority: '%REQ(:AUTHORITY)%'
                  bytes_received: '%BYTES_RECEIVED%'
                  bytes_sent: '%BYTES_SENT%'
                  connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                  downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                  downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                  duration: '%DURATION%'
                  method: '%REQ(:METHOD)%'
                  protocol: '%PROTOCOL%'
                  requested_server_name: '%REQUESTED_SERVER_NAME%'
                  response_code: '%RESPONSE_CODE%'
                  response_code_details: '%RESPONSE_CODE_DETAILS%'
                  response_flags: '%RESPONSE_FLAGS%'
                  route_name: '%ROUTE_NAME%'
                  start_time: '%START_TIME%'
                  upstream_cluster: '%UPSTREAM_CLUSTER%'
                  upstream_host: '%UPSTREAM_HOST%'
                  upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                  upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                  user-agent: '%REQ(USER-AGENT)%'
                  x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                  x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                  x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                  x-request-id: '%REQ(X-REQUEST-ID)%'
              path: /dev/stdout
          address:
            socketAddress:
              address: 0.0.0.0
              portValue: 8080
          defaultFilterChain:
            filters:
            - name: envoy.filters.network.http_connection_manager
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                accessLog:
                - name: envoy.access_loggers.file
                  typedConfig:
                    '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                    logFormat:
                      jsonFormat:
                        :authority: '%REQ(:AUTHORITY)%'
                        bytes_received: '%BYTES_RECEIVED%'
                        bytes_sent: '%BYTES_SENT%'
                        connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                        downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                        downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                        duration: '%DURATION%'
                        method: '%REQ(:METHOD)%'
                        protocol: '%PROTOCOL%'
                        requested_server_name: '%REQUESTED_SERVER_NAME%'
                        response_code: '%RESPONSE_CODE%'
                        response_code_details: '%RESPONSE_CODE_DETAILS%'
                        response_flags: '%RESPONSE_FLAGS%'
                        route_name: '%ROUTE_NAME%'
                        start_time: '%START_TIME%'
                        upstream_cluster: '%UPSTREAM_CLUSTER%'
                        upstream_host: '%UPSTREAM_HOST%'
                        upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                        upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                        user-agent: '%REQ(USER-AGENT)%'
                        x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                        x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                        x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                        x-request-id: '%REQ(X-REQUEST-ID)%'
                    path: /dev/stdout
                commonHttpProtocolOptions:
                  headersWithUnderscoresAction: REJECT_REQUEST
                http2ProtocolOptions:
                  initialConnectionWindowSize: 1048576
                  initialStreamWindowSize: 65536
                  maxConcurrentStreams: 100
                httpFilters:
                - name: envoy.filters.http.grpc_web
                  typedConfig:
                    '@type': type.googleapis.com/envoy.extensions.filters.http.grpc_web.v3.GrpcWeb
                - name: envoy.filters.http.grpc_stats
                  typedConfig:
                    '@type': type.googleapis.com/envoy.extensions.filters.http.grpc_stats.v3.FilterConfig
                    emitFilterState: true
                    statsForAllMethods: true
                - name: envoy.filters.http.router
                  typedConfig:
                    '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                    suppressEnvoyHeaders: true
                mergeSlashes: true
                normalizePath: true
                pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
                rds:
                  configSource:
                    ads: {}
                    resourceApiVersion: V3
                  routeConfigName: default/eg/grpc
                serverHeaderTransformation: PASS_THROUGH
                statPrefix: http-8080
                useRemoteAddress: true
            name: default/eg/grpc
          maxConnectionsToAcceptPerSocketEvent: 1
          name: default/eg/grpc
          perConnectionBufferLimitBytes: 32768
    - activeState:
        listener:
          '@type': type.googleapis.com/envoy.config.listener.v3.Listener
          accessLog:
          - filter:
              responseFlagFilter:
                flags:
                - NR
            name: envoy.access_loggers.file
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
              logFormat:
                jsonFormat:
                  :authority: '%REQ(:AUTHORITY)%'
                  bytes_received: '%BYTES_RECEIVED%'
                  bytes_sent: '%BYTES_SENT%'
                  connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                  downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                  downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                  duration: '%DURATION%'
                  method: '%REQ(:METHOD)%'
                  protocol: '%PROTOCOL%'
                  requested_server_name: '%REQUESTED_SERVER_NAME%'
                  response_code: '%RESPONSE_CODE%'
                  response_code_details: '%RESPONSE_CODE_DETAILS%'
                  response_flags: '%RESPONSE_FLAGS%'
                  route_name: '%ROUTE_NAME%'
                  start_time: '%START_TIME%'
                  upstream_cluster: '%UPSTREAM_CLUSTER%'
                  upstream_host: '%UPSTREAM_HOST%'
                  upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                  upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                  user-agent: '%REQ(USER-AGENT)%'
                  x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                  x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                  x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                  x-request-id: '%REQ(X-REQUEST-ID)%'
              path: /dev/stdout
          address:
            socketAddress:
              address: 0.0.0.0
              portValue: 1234
          filterChains:
          - filters:
            - name: envoy.filters.network.tcp_proxy
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy
                accessLog:
                - name: envoy.access_loggers.file
                  typedConfig:
                    '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                    logFormat:
                      jsonFormat:
                        :authority: '%REQ(:AUTHORITY)%'
                        bytes_received: '%BYTES_RECEIVED%'
                        bytes_sent: '%BYTES_SENT%'
                        connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                        downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                        downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                        duration: '%DURATION%'
                        method: '%REQ(:METHOD)%'
                        protocol: '%PROTOCOL%'
                        requested_server_name: '%REQUESTED_SERVER_NAME%'
                        response_code: '%RESPONSE_CODE%'
                        response_code_details: '%RESPONSE_CODE_DETAILS%'
                        response_flags: '%RESPONSE_FLAGS%'
                        route_name: '%ROUTE_NAME%'
                        start_time: '%START_TIME%'
                        upstream_cluster: '%UPSTREAM_CLUSTER%'
                        upstream_host: '%UPSTREAM_HOST%'
                        upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                        upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                        user-agent: '%REQ(USER-AGENT)%'
                        x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                        x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                        x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                        x-request-id: '%REQ(X-REQUEST-ID)%'
                    path: /dev/stdout
                cluster: tcproute/default/backend/rule/-1
                statPrefix: tcp-1234
            name: tcproute/default/backend
          maxConnectionsToAcceptPerSocketEvent: 1
          name: default/eg/tcp
          perConnectionBufferLimitBytes: 32768
    - activeState:
        listener:
          '@type': type.googleapis.com/envoy.config.listener.v3.Listener
          accessLog:
          - filter:
              responseFlagFilter:
                flags:
                - NR
            name: envoy.access_loggers.file
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
              logFormat:
                jsonFormat:
                  :authority: '%REQ(:AUTHORITY)%'
                  bytes_received: '%BYTES_RECEIVED%'
                  bytes_sent: '%BYTES_SENT%'
                  connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                  downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                  downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                  duration: '%DURATION%'
                  method: '%REQ(:METHOD)%'
                  protocol: '%PROTOCOL%'
                  requested_server_name: '%REQUESTED_SERVER_NAME%'
                  response_code: '%RESPONSE_CODE%'
                  response_code_details: '%RESPONSE_CODE_DETAILS%'
                  response_flags: '%RESPONSE_FLAGS%'
                  route_name: '%ROUTE_NAME%'
                  start_time: '%START_TIME%'
                  upstream_cluster: '%UPSTREAM_CLUSTER%'
                  upstream_host: '%UPSTREAM_HOST%'
                  upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                  upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                  user-agent: '%REQ(USER-AGENT)%'
                  x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                  x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                  x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                  x-request-id: '%REQ(X-REQUEST-ID)%'
              path: /dev/stdout
          address:
            socketAddress:
              address: 0.0.0.0
              portValue: 8443
          filterChains:
          - filterChainMatch:
              serverNames:
              - foo.com
            filters:
            - name: envoy.filters.network.tcp_proxy
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy
                accessLog:
                - name: envoy.access_loggers.file
                  typedConfig:
                    '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                    logFormat:
                      jsonFormat:
                        :authority: '%REQ(:AUTHORITY)%'
                        bytes_received: '%BYTES_RECEIVED%'
                        bytes_sent: '%BYTES_SENT%'
                        connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                        downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                        downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                        duration: '%DURATION%'
                        method: '%REQ(:METHOD)%'
                        protocol: '%PROTOCOL%'
                        requested_server_name: '%REQUESTED_SERVER_NAME%'
                        response_code: '%RESPONSE_CODE%'
                        response_code_details: '%RESPONSE_CODE_DETAILS%'
                        response_flags: '%RESPONSE_FLAGS%'
                        route_name: '%ROUTE_NAME%'
                        start_time: '%START_TIME%'
                        upstream_cluster: '%UPSTREAM_CLUSTER%'
                        upstream_host: '%UPSTREAM_HOST%'
                        upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                        upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                        user-agent: '%REQ(USER-AGENT)%'
                        x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                        x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                        x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                        x-request-id: '%REQ(X-REQUEST-ID)%'
                    path: /dev/stdout
                cluster: tlsroute/default/backend/rule/-1
                statPrefix: tls-passthrough-8443
            name: tlsroute/default/backend
          listenerFilters:
          - name: envoy.filters.listener.tls_inspector
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.filters.listener.tls_inspector.v3.TlsInspector
          maxConnectionsToAcceptPerSocketEvent: 1
          name: default/eg/tls-passthrough
          perConnectionBufferLimitBytes: 32768
    - activeState:
        listener:
          '@type': type.googleapis.com/envoy.config.listener.v3.Listener
          accessLog:
          - filter:
              responseFlagFilter:
                flags:
                - NR
            name: envoy.access_loggers.file
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
              logFormat:
                jsonFormat:
                  :authority: '%REQ(:AUTHORITY)%'
                  bytes_received: '%BYTES_RECEIVED%'
                  bytes_sent: '%BYTES_SENT%'
                  connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                  downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                  downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                  duration: '%DURATION%'
                  method: '%REQ(:METHOD)%'
                  protocol: '%PROTOCOL%'
                  requested_server_name: '%REQUESTED_SERVER_NAME%'
                  response_code: '%RESPONSE_CODE%'
                  response_code_details: '%RESPONSE_CODE_DETAILS%'
                  response_flags: '%RESPONSE_FLAGS%'
                  route_name: '%ROUTE_NAME%'
                  start_time: '%START_TIME%'
                  upstream_cluster: '%UPSTREAM_CLUSTER%'
                  upstream_host: '%UPSTREAM_HOST%'
                  upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                  upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                  user-agent: '%REQ(USER-AGENT)%'
                  x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                  x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                  x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                  x-request-id: '%REQ(X-REQUEST-ID)%'
              path: /dev/stdout
          address:
            socketAddress:
              address: 0.0.0.0
              portValue: 1234
              protocol: UDP
          listenerFilters:
          - name: envoy.filters.udp_listener.udp_proxy
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.filters.udp.udp_proxy.v3.UdpProxyConfig
              accessLog:
              - name: envoy.access_loggers.file
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                  logFormat:
                    jsonFormat:
                      :authority: '%REQ(:AUTHORITY)%'
                      bytes_received: '%BYTES_RECEIVED%'
                      bytes_sent: '%BYTES_SENT%'
                      connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                      downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                      downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                      duration: '%DURATION%'
                      method: '%REQ(:METHOD)%'
                      protocol: '%PROTOCOL%'
                      requested_server_name: '%REQUESTED_SERVER_NAME%'
                      response_code: '%RESPONSE_CODE%'
                      response_code_details: '%RESPONSE_CODE_DETAILS%'
                      response_flags: '%RESPONSE_FLAGS%'
                      route_name: '%ROUTE_NAME%'
                      start_time: '%START_TIME%'
                      upstream_cluster: '%UPSTREAM_CLUSTER%'
                      upstream_host: '%UPSTREAM_HOST%'
                      upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                      upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                      user-agent: '%REQ(USER-AGENT)%'
                      x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                      x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                      x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                      x-request-id: '%REQ(X-REQUEST-ID)%'
                  path: /dev/stdout
              matcher:
                onNoMatch:
                  action:
                    name: route
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.udp.udp_proxy.v3.Route
                      cluster: udproute/default/backend/rule/-1
              statPrefix: service
          name: default/eg/udp
